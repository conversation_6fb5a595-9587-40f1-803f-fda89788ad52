<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文点评 - 慧习作</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
            color: #323842;
            line-height: 1.6;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #ffffff;
            box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
            min-height: 100vh;
        }

        /* Header */
        .header {
            position: relative;
            height: 40px;
            border-bottom: 1px solid #BCC1CA;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-button {
            position: absolute;
            left: 24px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-title {
            font-size: 16px;
            font-weight: 400;
            color: #323842;
        }

        .header-icons {
            position: absolute;
            right: 0;
            top: 0;
            height: 40px;
            display: flex;
        }

        .icon-container {
            width: 72px;
            height: 40px;
            border-left: 1px solid #BCC1CA;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Navigation Tabs */
        .nav-tabs {
            height: 40px;
            background: transparent;
            display: flex;
            margin: 0 23px;
            margin-top: 8px;
        }

        .nav-tab {
            flex: 1;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            color: #565E6C;
            cursor: pointer;
            position: relative;
        }

        .nav-tab.active {
            color: #636AE8;
            font-weight: 700;
        }

        .nav-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #636AE8;
        }

        /* Content */
        .content {
            padding: 0;
        }

        .info-card {
            background: #F8F9FA;
            border-bottom: 1px solid #BCC1CA;
            padding: 11px 17px;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;
        }

        .info-item {
            font-size: 11px;
            color: #323842;
        }

        .score {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .score-number {
            font-size: 24px;
            color: #DE3B40;
        }

        .score-unit {
            font-size: 12px;
            color: #DE3B40;
        }

        /* Section */
        .section {
            background: #F8F9FA;
            border-bottom: 1px solid #BCC1CA;
            padding: 11px 17px;
        }

        .section-title {
            font-size: 11px;
            color: #323842;
            margin-bottom: 8px;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 18px;
            margin-bottom: 8px;
        }

        .stars {
            display: flex;
            gap: 0;
        }

        .star {
            width: 18px;
            height: 18px;
            background-image: url('./images/star1.svg');
            background-size: contain;
            background-repeat: no-repeat;
        }

        .comment-label {
            font-size: 11px;
            color: #323842;
            margin-bottom: 8px;
        }

        .comment-text {
            font-size: 11px;
            color: #323842;
            line-height: 1.6;
        }

        .summary-section {
            background: #F8F9FA;
            border-bottom: 1px solid #BCC1CA;
            padding: 11px 17px;
        }

        .summary-text {
            font-size: 11px;
            color: #323842;
            line-height: 1.6;
            margin-top: 8px;
        }

        .section-header {
            font-size: 14px;
            color: #171A1F;
            margin: 16px 17px 8px;
            font-weight: 400;
        }

        .error-section {
            background: #F8F9FA;
            border-bottom: 1px solid #BCC1CA;
            padding: 11px 17px;
        }

        .error-count {
            font-size: 11px;
            color: #323842;
            margin-bottom: 8px;
        }

        .error-list {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            font-size: 11px;
            color: #323842;
        }

        .timestamp {
            font-size: 11px;
            color: #323842;
            margin: 16px 17px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="back-button" onclick="goBack()">
                <img src="./images/arrow_left2.svg" alt="返回" style="width: 17px; height: 12px;">
            </div>
            <div class="header-title">详情</div>
            <div class="header-icons">
                <div class="icon-container">
                    <img src="./images/vector1.svg" alt="图标1" style="width: 27px; height: 11px;">
                </div>
                <div class="icon-container">
                    <img src="./images/vector5.svg" alt="图标2" style="width: 66px; height: 11px;">
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <div class="nav-tab" onclick="switchTab('requirement')">作文要求</div>
            <div class="nav-tab active" onclick="switchTab('review')">作文点评</div>
            <div class="nav-tab" onclick="switchTab('report')">作文报告</div>
            <div class="nav-tab" onclick="switchTab('polish')">润色范文</div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Basic Info -->
            <div class="info-card">
                <div class="info-item">第一单元</div>
                <div class="info-item">单元作文</div>
                <div class="info-item">全命题</div>
                <div class="info-item" style="width: 100%; margin-top: 8px;">我的植物朋友</div>
                <div class="score">
                    <span class="score-number">28</span>
                    <span class="score-unit">分</span>
                </div>
            </div>

            <!-- Review Section Header -->
            <div class="section-header">点评</div>

            <!-- 思想与中心 -->
            <div class="section">
                <div class="section-title">思想与中心</div>
                <div class="rating">
                    <div class="stars">
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
                <div class="comment-label">评语</div>
                <div class="comment-text">文章紧扣'未来的城市生活'主题，通过多个方面描绘了未来城市的科技发展和生活变化，中心思想明确。</div>
            </div>

            <!-- 内容 -->
            <div class="section">
                <div class="section-title">内容</div>
                <div class="rating">
                    <div class="stars">
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
                <div class="comment-label">评语</div>
                <div class="comment-text">内容具体详细，涵盖了建筑、交通、环保、教育和医疗等多个领域，展现了未来城市生活的全面图景。</div>
            </div>

            <!-- 结构 -->
            <div class="section">
                <div class="section-title">结构</div>
                <div class="rating">
                    <div class="stars">
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
                <div class="comment-label">评语</div>
                <div class="comment-text">文章结构较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。</div>
            </div>

            <!-- 语言 -->
            <div class="section">
                <div class="section-title">语言</div>
                <div class="rating">
                    <div class="stars">
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
                <div class="comment-label">评语</div>
                <div class="comment-text">语句通顺</div>
            </div>

            <!-- 总结评语 -->
            <div class="summary-section">
                <div class="section-title">总结评语</div>
                <div class="summary-text">本文围绕'未来的城市生活'这一主题，从建筑、交通、环保、教育和医疗等多个方面展开，描绘了一个科技高度发达的未来城市图景。文章内容具体，涵盖了多个领域，展现了未来城市生活的全面变化。结构上，文章层次较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。语言方面，文章语句基本通顺，但存在个别错别字和标点符号使用不当的问题，修辞手法的运用也较为有限。总体而言，文章紧扣主题，中心思想明确，内容具体，结构较为清晰，语言基本流畅，但仍有提升空间。</div>
            </div>

            <!-- 字数不足 -->
            <div class="section">
                <div class="section-title">字数不足</div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <span class="comment-text">扣2分</span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span class="comment-text">总字数</span>
                    <span class="comment-text">100字</span>
                </div>
            </div>

            <!-- 不足之处 -->
            <div class="section-header">不足之处</div>

            <!-- 错别字 -->
            <div class="error-section">
                <div class="section-title">错别字</div>
                <div class="error-count">共10处</div>
                <div class="error-list">
                    <div>1.棋中（其中）</div>
                    <div>2.坐号（座号）</div>
                    <div>3.坐号（座号）</div>
                    <div>4.坐号（座号）</div>
                    <div>5.棋中（其中）</div>
                    <div>6.坐号（座号）</div>
                    <div>7.坐号（座号）</div>
                    <div>8.坐号（座号）</div>
                    <div>9.棋中（其中）</div>
                    <div>10.坐号（座号）</div>
                </div>
            </div>

            <!-- 滥用拼音 -->
            <div class="error-section">
                <div class="section-title">滥用拼音</div>
                <div class="error-count">共10处</div>
                <div class="error-list">
                    <div>1.qi（其）</div>
                    <div>2.zuo（座）</div>
                    <div>3.zuo（座）</div>
                    <div>4.zuo（座）</div>
                    <div>5.zuo（座）</div>
                    <div>6.zuo（座）</div>
                    <div>7.zuo（座）</div>
                    <div>8.zuo（座）</div>
                    <div>9.zuo（座）</div>
                    <div>10.zuo（座）</div>
                </div>
            </div>

            <!-- 时间戳 -->
            <div class="timestamp">最后批改时间：2025.07.16  15:46</div>
        </div>
    </div>

    <script>
        function goBack() {
            // 返回上一页或跳转到首页
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'dashboard.html';
            }
        }

        function switchTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加active类到当前点击的tab
            event.target.classList.add('active');
            
            // 根据tab切换内容（这里可以添加具体的切换逻辑）
            switch(tabName) {
                case 'requirement':
                    // 跳转到作文要求页面
                    console.log('切换到作文要求');
                    break;
                case 'review':
                    // 当前页面
                    console.log('切换到作文点评');
                    break;
                case 'report':
                    // 跳转到作文报告页面
                    console.log('切换到作文报告');
                    break;
                case 'polish':
                    // 跳转到润色范文页面
                    console.log('切换到润色范文');
                    break;
            }
        }
    </script>
</body>
</html>
